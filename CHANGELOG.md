# CHANGELOG.md


## [v1.1.0] - 2025-06-28

### Added
- ML-based Smart Response Generator for negotiations.
  - Trained intent classifier (TF-IDF + LogisticRegression) to predict negotiation intent (e.g., propose, accept, decline).
  - Introduced `ml/train_intent_classifier.py` for model training.
  - Created `ml/models/negotiation_classifier.pkl` for use in prediction.
- FastAPI endpoint:
  - `POST /negotiations/suggest/` to suggest smart replies based on chat history and user role.
- New service module:
  - `services/nlp_response_generator.py` handles suggestion logic and reply templating.
- React component:
  - `NegotiationAssistant.jsx` for fetching and displaying suggestions in the chat UI.
  - Enables one-click insertion of smart reply messages for buyers/sellers.

### 📚 Documentation
- Updated system architecture with ML augmentation flow.
- Added section in README under "Features Implemented" for Smart Negotiation Assistant.


## [v1.1.0] - 2025-06-27

### Added
- Integrated full ML training pipeline using LightGBM:
  - `train_model.py` for enhanced dataset
  - `predict.py` for model inference per buyer
- Added derived features in `feature_analysis.py`:
  - `days_since_listed`, `seller_rating_avg`, `tag_similarity`, `norm_price`, `price_x_quantity`
- Included SHAP explainability via `shap_analysis.py` with PNG output
- Centralized ML path configuration in `ml/config.py`
- Automatic directory creation using `ensure_directories()`
- Log training metadata to:
  - `train_log.json`
  - `train_log_history.jsonl`
- Visual training log viewer `view_train_log.py` with table and accuracy trend plot

### Changed
- Reorganized script execution using `train_model.py` and `predict.py` with safe PYTHONPATH setup
- Resolved LightGBM feature mismatch error by aligning training and prediction schemas


## [v1.0.1] - 2025-06-25
```
- Added functionlaity for My Produce Listings ( Farmer only)
- Added functionlaity for adding produces ( Farmer only)
- Added functionlaity for deleting produces ( Farmer only)
- Added functionlaity for updating produces ( Farmer only)
    http://localhost:3000/produce
```


## [v1.0.0] - 2025-06-22
### ✨ Added
- Implemented 10 diverse recommendation strategies:
  - Content-Based Filtering (`/recommendations/produce`)
  - General Recommendations (`/recommendations/general`)
  - Collaborative Filtering (`/recommendations/collaborative`)
  - Hybrid Recommendation Strategy (`/recommendations/hybrid`)
  - Trending Produce Based on Feedback Count (`/recommendations/trending`)
  - Feedback-Based Recommendations (`/recommendations/feedback`)
  - Negotiation-Based Recommendations (`/recommendations/negotiation`)
  - Preference-Based Recommendations (Placeholder) (`/recommendations/preferences`)
  - Location-Based Filtering (`/recommendations/location`)
  - Price Range-Based Filtering (`/recommendations/price`)

- Integrated fallback error handling in all service methods.
- Connected each strategy to corresponding FastAPI routes with query parameter support.
- Created visual RecSys strategy diagram for documentation and presentations.


### v1.0.0 (21.06.2025)
```
- Added functionality for sending broadcasting messages to all users (admin only)
- Added functionality for listing all broadcasts (All user)
- Added functionality for deleting broadcasts (admin only)
- Added functionality for updating broadcasts (admin only)
- Added testapi for LoginTest , CorsTest , TestRegister , BroadcastTest
  http://localhost:3000/test-register
  http://localhost:3000/cors-test
  http://localhost:3000/login-test
  http://localhost:3000/broadcast-test
  
- Schema change to add 'admin' role to table users (column: role)

 ### API Endpoints Available
- `GET /broadcast/` - List all broadcasts (all authenticated users)
- `POST /broadcast/` - Send broadcast (admin only)
- `GET /broadcast/{id}` - Get specific broadcast (all authenticated users)  
- `PUT /broadcast/{id}` - Update broadcast (admin only)
- `DELETE /broadcast/{id}` - Delete broadcast (admin only)
 
 ## Alternate Option: Update Database Constraint
Connect to your database and run:
-- Remove existing constraint
ALTER TABLE users DROP CONSTRAINT check_role_valid;

-- Add new constraint that includes admin
ALTER TABLE users ADD CONSTRAINT check_role_valid 
CHECK (role IN ('farmer', 'buyer', 'admin'));

```