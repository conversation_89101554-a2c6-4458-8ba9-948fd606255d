# Negotiation System Implementation

This document describes the comprehensive negotiation system implemented for the AI-Powered-Agri-Commerce platform.

## Features Implemented

### 1. Produce Market Page (`/produce-market`)
- **Accessible to all roles**: Farmers, Buyers, and Admins can view all produce listings
- **Search and Filter**: Search by crop, category, or location
- **Sort Options**: Latest, Price (Low to High), Price (High to Low), Quantity Available
- **Conversation Button**: Each produce listing has a "CONVERSATION" button to start negotiations
- **Responsive Design**: Works on desktop and mobile devices

### 2. Negotiation Chat Interface (`/negotiation/:produceId`)
- **Real-time Chat**: Chat-style interface for negotiations between farmers and buyers
- **Message Types**: Propose, Accept, Decline, Reject
- **Offer Details**: Include price, quantity, and delivery location in proposals
- **Summary Sidebar**: Shows negotiation summary with IDs, participants, status, and dates
- **Message History**: Complete conversation history with timestamps
- **Negotiation Assistant**: AI-powered suggestions for responses

### 3. Negotiation History (`/negotiations/history`)
- **User's Negotiations**: View all negotiations the user is involved in
- **Status Filtering**: Filter by All, Pending, Accepted, Declined, Rejected
- **Detailed Information**: Shows negotiation ID, produce details, participants, pricing, and status
- **Quick Access**: Direct links to continue conversations

### 4. Enhanced Backend API

#### New Endpoints:
- `POST /negotiations/` - Create new negotiation message
- `GET /negotiations/produce/{produce_id}` - Get all negotiations for a produce
- `GET /negotiations/produce/{produce_id}/details` - Get detailed negotiations with user/produce info
- `GET /negotiations/history` - Get user's negotiation history
- `GET /negotiations/summary/{negotiation_id}` - Get negotiation summary

#### Enhanced Data Models:
- **Negotiation Model**: Added `offered_price`, `offered_quantity`, `delivery_location` fields
- **New Schemas**: `NegotiationWithDetails`, `NegotiationSummary`, `NegotiationStatus`
- **Enhanced Services**: Better conversation threading and status management

## Database Changes

### New Columns in `negotiations` table:
- `offered_price` (FLOAT) - Price offered in the negotiation
- `offered_quantity` (FLOAT) - Quantity being negotiated
- `delivery_location` (TEXT) - Proposed delivery location

### New Enum Values:
- Added `reject` to negotiation types (propose, accept, decline, reject)

## Installation and Setup

### 1. Run Database Migration
```bash
cd backend
python migration_add_negotiation_fields.py
```

### 2. Install Frontend Dependencies
```bash
cd frontend
npm install
```

### 3. Start the Application
```bash
# Backend
cd backend
uvicorn app.main:app --reload

# Frontend
cd frontend
npm start
```

## Usage Flow

### For Buyers:
1. Visit `/produce-market` to browse all available produce
2. Click "CONVERSATION" button on any produce to start negotiation
3. Send messages with offers including price, quantity, and delivery details
4. View negotiation history at `/negotiations/history`
5. Continue conversations until agreement is reached

### For Farmers:
1. List produce through existing produce management system
2. Receive negotiation requests from buyers
3. Respond to offers with counter-proposals, accept, or decline
4. Track all negotiations through history page
5. Manage multiple concurrent negotiations

### For Admins:
1. View all produce listings in the market
2. Monitor negotiation activities (read-only access)
3. Access system-wide negotiation statistics

## Key Features Matching Requirements

✅ **Produce listing screen visible to all roles**
- Implemented in `/produce-market` with role-based access

✅ **Conversation buttons for negotiations**
- Each produce card has a "CONVERSATION" button

✅ **Chat functionality between farmers and buyers**
- Real-time chat interface with message types and offer details

✅ **Negotiation history tracking**
- Complete history with filtering and status tracking

✅ **Status screens showing negotiation summaries**
- Summary sidebar in chat and detailed history page

## Technical Architecture

### Frontend Components:
- `ProduceMarket.jsx` - Main marketplace listing
- `NegotiationChat.jsx` - Chat interface for negotiations
- `NegotiationHistory.jsx` - User's negotiation history
- `NegotiationAssistant.jsx` - AI-powered response suggestions

### Backend Services:
- Enhanced `negotiation_service.py` - Core negotiation logic
- Updated `produce_service.py` - Include farmer information
- New API endpoints in `negotiation.py` router

### Database Models:
- Enhanced `Negotiation` model with offer details
- Added relationships between User, Produce, and Negotiation models

## Future Enhancements

1. **Real-time Notifications**: WebSocket integration for instant message delivery
2. **File Attachments**: Support for images and documents in negotiations
3. **Contract Generation**: Automatic contract creation upon agreement
4. **Payment Integration**: Secure payment processing for completed deals
5. **Rating System**: Post-negotiation feedback and ratings
6. **Advanced Analytics**: Negotiation success rates and market insights

## Testing

### Manual Testing Checklist:
- [ ] Browse produce market as different user roles
- [ ] Start negotiations from produce listings
- [ ] Send different types of messages (propose, accept, decline, reject)
- [ ] Include offer details (price, quantity, delivery)
- [ ] View negotiation history with proper filtering
- [ ] Navigate between market, chat, and history pages
- [ ] Test responsive design on mobile devices

### API Testing:
- [ ] Test all new negotiation endpoints
- [ ] Verify proper authentication and authorization
- [ ] Test error handling for invalid requests
- [ ] Validate data schemas and relationships

## Support

For issues or questions regarding the negotiation system:
1. Check the console logs for error messages
2. Verify database migration was successful
3. Ensure all API endpoints are accessible
4. Test with different user roles and scenarios
