import AuthForm from "../components/AuthForm";
import { authAPI } from "../utils/api";
import { useLocation } from "react-router-dom";

export default function Login() {
  const location = useLocation();

  const handleLogin = async (formData) => {
    try {
      console.log("Login form data received:", formData);

      // Validate required fields
      if (!formData.phone_number || !formData.password) {
        alert("Phone number and password are required");
        return;
      }

      // Convert to form data format expected by backend
      const loginData = new URLSearchParams();
      loginData.append('username', formData.phone_number); // Backend expects username field (phone_number)
      loginData.append('password', formData.password);

      console.log("Sending login data:", loginData.toString());

      const res = await authAPI.login(loginData);
      console.log("Login response:", res);

      localStorage.setItem("token", res.access_token);

      // Store user info for easy access
      const userInfo = {
        user_id: res.user_id,
        name: res.name,
        role: res.role,
        phone_number: res.phone_number,
        location: res.location
      };
      localStorage.setItem("userInfo", JSON.stringify(userInfo));

      alert("Login successful");

      // Redirect to return URL or home page
      const returnUrl = location.state?.returnUrl || "/";
      window.location.href = returnUrl;
    } catch (err) {
      console.error("Login error:", err);

      let errorMessage = "Login failed";
      if (err.response?.data?.detail) {
        errorMessage = err.response.data.detail;
      } else if (err.message) {
        errorMessage = err.message;
      }

      alert("Login failed: " + errorMessage);
    }
  };

  return (
    <div>
      {location.state?.message && (
        <div style={{
          backgroundColor: "#d1ecf1",
          color: "#0c5460",
          padding: "12px",
          borderRadius: "6px",
          margin: "20px auto",
          maxWidth: "400px",
          border: "1px solid #bee5eb",
          textAlign: "center"
        }}>
          {location.state.message}
        </div>
      )}
      <AuthForm onSubmit={handleLogin} type="login" />
    </div>
  );
}
