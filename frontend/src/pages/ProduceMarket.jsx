import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { produceAPI } from '../utils/api';
import './ProduceMarket.css';

const ProduceCard = ({ produce, currentUser, onStartNegotiation }) => {
  return (
    <div className="produce-card">
      <div className="produce-image">
        {produce.image_url ? (
          <img src={produce.image_url} alt={produce.crop} />
        ) : (
          <div className="no-image">📦</div>
        )}
      </div>
      
      <div className="produce-details">
        <h3 className="produce-title">{produce.crop}</h3>
        <p className="produce-category">Category: {produce.category}</p>
        <p className="produce-grade">Grade: {produce.grade}</p>
        <div className="produce-pricing">
          <span className="price">₹{produce.price_per_unit}/{produce.unit}</span>
          <span className="quantity">{produce.quantity} {produce.unit} available</span>
        </div>
        
        {produce.location && (
          <p className="produce-location">📍 {produce.location}</p>
        )}
        
        {produce.description && (
          <p className="produce-description">{produce.description}</p>
        )}
        
        <div className="produce-farmer">
          <span>Farmer: {produce.farmer_name || 'Unknown'}</span>
        </div>
        
        <div className="produce-actions">
          {currentUser && currentUser.role !== 'admin' && currentUser.user_id !== produce.farmer_id && (
            <button
              onClick={() => onStartNegotiation(produce.id)}
              className="conversation-btn"
            >
              💬 CONVERSATION
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

const ProduceMarket = () => {
  const [produces, setProduces] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentUser, setCurrentUser] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [sortBy, setSortBy] = useState('latest');
  
  const navigate = useNavigate();

  useEffect(() => {
    // Get current user from local storage (authentication is handled by ProtectedRoute)
    const userInfo = localStorage.getItem('userInfo');
    if (userInfo) {
      try {
        setCurrentUser(JSON.parse(userInfo));
      } catch (err) {
        console.error('Error parsing user info:', err);
      }
    }

    fetchProduces();
  }, []);

  const fetchProduces = async () => {
    try {
      setLoading(true);
      const data = await produceAPI.getAllProduce();
      setProduces(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch produce listings. Please try again.');
      console.error('Error fetching produces:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleStartNegotiation = (produceId) => {
    navigate(`/negotiation/${produceId}`);
  };

  // Filter and sort produces
  const filteredProduces = produces
    .filter(produce => {
      const matchesSearch = produce.crop.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           produce.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (produce.location && produce.location.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesCategory = !categoryFilter || produce.category === categoryFilter;
      return matchesSearch && matchesCategory;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return a.price_per_unit - b.price_per_unit;
        case 'price-high':
          return b.price_per_unit - a.price_per_unit;
        case 'quantity':
          return b.quantity - a.quantity;
        case 'latest':
        default:
          return new Date(b.listing_date || 0) - new Date(a.listing_date || 0);
      }
    });

  // Get unique categories for filter
  const categories = [...new Set(produces.map(p => p.category))];

  if (loading) {
    return (
      <div className="produce-market">
        <div className="loading">Loading produce listings...</div>
      </div>
    );
  }

  return (
    <div className="produce-market">
      <div className="market-header">
        <h1>🛒 Produce Market</h1>
        <p>Browse fresh produce from farmers across the region</p>
      </div>

      {error && (
        <div className="error-message">
          {error}
          <button onClick={fetchProduces} className="retry-btn">Retry</button>
        </div>
      )}

      <div className="market-filters">
        <div className="search-box">
          <input
            type="text"
            placeholder="Search by crop, category, or location..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
        
        <div className="filter-controls">
          <select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            className="category-filter"
          >
            <option value="">All Categories</option>
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
          
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="sort-filter"
          >
            <option value="latest">Latest First</option>
            <option value="price-low">Price: Low to High</option>
            <option value="price-high">Price: High to Low</option>
            <option value="quantity">Quantity Available</option>
          </select>
        </div>
      </div>

      <div className="market-stats">
        <span>{filteredProduces.length} produce listings found</span>
      </div>

      {filteredProduces.length === 0 ? (
        <div className="no-produces">
          <h3>No produce listings found</h3>
          <p>Try adjusting your search or filter criteria</p>
        </div>
      ) : (
        <div className="produces-grid">
          {filteredProduces.map((produce) => (
            <ProduceCard
              key={produce.id}
              produce={produce}
              currentUser={currentUser}
              onStartNegotiation={handleStartNegotiation}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default ProduceMarket;
