.negotiation-chat {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.chat-header {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 20px;
}

.back-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.back-btn:hover {
  background-color: #5a6268;
}

.produce-info h2 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.produce-info p {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.chat-container {
  display: flex;
  gap: 20px;
  flex: 1;
  min-height: 0;
}

.negotiation-summary {
  width: 300px;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  height: fit-content;
  border: 1px solid #e9ecef;
}

.negotiation-summary h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.summary-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.summary-row:last-child {
  border-bottom: none;
}

.summary-row span:first-child {
  font-weight: 500;
  color: #495057;
}

.summary-row span:last-child {
  color: #6c757d;
  text-align: right;
}

.status-accepted {
  background-color: #d4edda;
  color: #155724;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.chat-messages {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.loading {
  text-align: center;
  padding: 50px;
  color: #7f8c8d;
  font-size: 1.2rem;
}

.error {
  text-align: center;
  padding: 50px;
  color: #e74c3c;
  font-size: 1.2rem;
}

.error-message {
  background-color: #fee;
  border: 1px solid #fcc;
  color: #c33;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.retry-btn {
  background-color: #e74c3c;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.no-messages {
  text-align: center;
  padding: 40px;
  color: #7f8c8d;
}

.message {
  max-width: 70%;
  padding: 15px;
  border-radius: 12px;
  margin-bottom: 10px;
  position: relative;
}

.message-own {
  align-self: flex-end;
  background-color: #007bff;
  color: white;
}

.message-other {
  align-self: flex-start;
  background-color: #f8f9fa;
  color: #333;
  border: 1px solid #e9ecef;
}

.message-propose {
  border-left: 4px solid #007bff;
}

.message-accept {
  border-left: 4px solid #28a745;
}

.message-decline {
  border-left: 4px solid #ffc107;
}

.message-reject {
  border-left: 4px solid #dc3545;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.sender-name {
  font-weight: 600;
}

.message-time {
  opacity: 0.7;
}

.message-type {
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: 8px;
  opacity: 0.8;
}

.message-content {
  line-height: 1.4;
  margin-bottom: 8px;
}

.message-offer-details {
  font-size: 12px;
  opacity: 0.9;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 8px;
  margin-top: 8px;
}

.message-offer-details div {
  margin-bottom: 2px;
}

.message-form {
  border-top: 1px solid #e9ecef;
  padding: 20px;
  background-color: #f8f9fa;
}

.message-input-container {
  margin-bottom: 15px;
}

.message-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  resize: vertical;
  min-height: 80px;
}

.message-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
}

.offer-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.offer-row {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.offer-row label {
  font-size: 12px;
  font-weight: 600;
  color: #495057;
}

.offer-type-select,
.offer-input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.send-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.send-btn:hover:not(:disabled) {
  background-color: #0056b3;
}

.send-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .chat-container {
    flex-direction: column;
  }
  
  .negotiation-summary {
    width: 100%;
    order: 2;
  }
  
  .chat-messages {
    order: 1;
    min-height: 400px;
  }
  
  .offer-details {
    grid-template-columns: 1fr;
  }
  
  .message {
    max-width: 85%;
  }
}
