import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

// Modified ProduceCard component with Conversation button
const ProduceCard = ({ produce, currentUser }) => {
  const navigate = useNavigate();

  const startNegotiation = (produceId) => {
    navigate(`/negotiation/${produceId}`);
  };

  return (
    <div className="border p-4 flex gap-4 items-start rounded-md shadow-sm mb-4 bg-white">
      <div className="w-24 h-24 bg-gray-200 flex items-center justify-center text-gray-600 rounded-md">
        {produce.image_url ? (
          <img src={produce.image_url} alt={produce.crop} className="w-full h-full object-cover rounded-md" />
        ) : (
          <span>No Image</span>
        )}
      </div>
      <div className="flex-grow">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="text-lg font-semibold">{produce.crop}</h3>
            <p className="text-sm text-gray-500">{produce.category}</p>
            <p className="text-sm">
              ₹{produce.price_per_unit} / {produce.unit} • {produce.quantity} {produce.unit} available
            </p>
            <p className="text-sm">Grade: {produce.grade}</p>
            <p className="text-sm text-gray-700 mt-1">{produce.description || 'No description available'}</p>
            {produce.location && <p className="text-sm mt-1">Location: {produce.location}</p>}
          </div>
          <div className="text-sm text-gray-500">
            <span className={`px-2 py-1 rounded ${produce.status === 'available' ? 'bg-green-100 text-green-800' : 'bg-gray-100'}`}>
              {produce.status}
            </span>
          </div>
        </div>

        <div className="flex gap-2 mt-4">
          {currentUser && currentUser.role !== 'admin' && (
            <button
              onClick={() => startNegotiation(produce.id)}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-300"
            >
              CONVERSATION
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

const ProduceListingPage = () => {
  const [produces, setProduces] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentUser, setCurrentUser] = useState(null);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    // Get current user from local storage
    const userInfo = localStorage.getItem('userInfo');
    if (userInfo) {
      setCurrentUser(JSON.parse(userInfo));
    }

    // Fetch all produces
    const fetchProduces = async () => {
      try {
        setLoading(true);
        const token = localStorage.getItem('token');

        // Check if token exists, if not redirect to login
        if (!token) {
          navigate('/login', { state: { returnUrl: '/produce-market' } });
          return;
        }

        // Corrected API endpoint without /api prefix
        const response = await axios.get('/produces', {
          headers: { Authorization: `Bearer ${token}` }
        });

        setProduces(response.data);
        setLoading(false);
      } catch (err) {
        setError('Failed to fetch produce listings. Please make sure you are logged in.');
        setLoading(false);
        console.error('Error fetching produces:', err);
      }
    };

    fetchProduces();
  }, [navigate]);

  if (loading) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-6">Produce Listings</h1>
        <div className="flex justify-center items-center h-64">
          <p>Loading produce listings...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-6">Produce Listings</h1>
        <div className="bg-red-100 text-red-700 p-4 rounded">
          {error}
          <div className="mt-4">
            <button
              onClick={() => navigate('/login')}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Login to View Produce
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 bg-gray-50 min-h-screen">
      <h1 className="text-2xl font-bold mb-6">Produce Listings</h1>

      {produces.length === 0 ? (
        <p>No produce listings available at the moment.</p>
      ) : (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
          {produces.map(produce => (
            <ProduceCard
              key={produce.id}
              produce={produce}
              currentUser={currentUser}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default ProduceListingPage;
