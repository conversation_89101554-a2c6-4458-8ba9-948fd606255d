.negotiation-history {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.history-header {
  text-align: center;
  margin-bottom: 30px;
}

.history-header h1 {
  color: #2c3e50;
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.history-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
  margin-bottom: 20px;
}

.back-to-market-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.back-to-market-btn:hover {
  background-color: #2980b9;
}

.loading {
  text-align: center;
  padding: 50px;
  font-size: 1.2rem;
  color: #7f8c8d;
}

.error-message {
  background-color: #fee;
  border: 1px solid #fcc;
  color: #c33;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.retry-btn {
  background-color: #e74c3c;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.retry-btn:hover {
  background-color: #c0392b;
}

.history-filters {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.filter-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

.filter-btn {
  background-color: white;
  border: 2px solid #e9ecef;
  color: #495057;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.filter-btn:hover {
  border-color: #3498db;
  color: #3498db;
}

.filter-btn.active {
  background-color: #3498db;
  border-color: #3498db;
  color: white;
}

.no-negotiations {
  text-align: center;
  padding: 60px 20px;
  color: #7f8c8d;
}

.no-negotiations h3 {
  margin-bottom: 10px;
  color: #5a6c7d;
}

.start-negotiating-btn {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 20px;
  transition: background-color 0.2s;
}

.start-negotiating-btn:hover {
  background-color: #218838;
}

.negotiations-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.negotiation-card {
  background-color: white;
  border: 1px solid #e1e8ed;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.negotiation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.negotiation-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.negotiation-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.negotiation-title h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.4rem;
}

.category-badge {
  background-color: #e9ecef;
  color: #495057;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-accepted {
  background-color: #d4edda;
  color: #155724;
}

.status-declined {
  background-color: #f8d7da;
  color: #721c24;
}

.status-rejected {
  background-color: #f5c6cb;
  color: #721c24;
}

.negotiation-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-row .label {
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.detail-row .value {
  color: #6c757d;
  font-size: 14px;
  text-align: right;
}

.pricing-info {
  background-color: #f1f3f4;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.price-row:last-child {
  margin-bottom: 0;
}

.price-row .label {
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.original-price {
  color: #6c757d;
  font-size: 14px;
}

.offer-price {
  color: #28a745;
  font-weight: 600;
  font-size: 16px;
}

.quantity,
.delivery {
  color: #495057;
  font-size: 14px;
}

.negotiation-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.view-negotiation-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.view-negotiation-btn:hover {
  background-color: #0056b3;
}

.pending-indicator {
  color: #856404;
  font-size: 14px;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .negotiation-history {
    padding: 15px;
  }
  
  .history-header h1 {
    font-size: 2rem;
  }
  
  .filter-buttons {
    justify-content: flex-start;
  }
  
  .negotiation-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .negotiation-details {
    grid-template-columns: 1fr;
  }
  
  .negotiation-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .view-negotiation-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .negotiation-card {
    padding: 16px;
  }
  
  .detail-row,
  .price-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .detail-row .value,
  .price-row .original-price,
  .price-row .offer-price {
    text-align: left;
  }
}
