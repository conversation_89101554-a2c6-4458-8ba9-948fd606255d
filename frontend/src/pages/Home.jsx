import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";

export default function Home() {
  const [userRole, setUserRole] = useState("");
  const [errorMessage, setErrorMessage] = useState(null);
  const location = useLocation();

  useEffect(() => {
    // Check for error messages from navigation
    if (location.state?.error) {
      setErrorMessage(location.state.error);
      // Clear the error from location state
      window.history.replaceState({}, document.title);
    }

    // Check user role from token
    const token = localStorage.getItem("token");
    if (token) {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        setUserRole(payload.role);
      } catch (err) {
        console.error("Error parsing token:", err);
      }
    }
  }, [location]);

  return (
    <div style={{ padding: "20px", textAlign: "center" }}>
      <h2>Welcome! You're logged in.</h2>

      {errorMessage && (
        <div style={{
          backgroundColor: "#f8d7da",
          color: "#721c24",
          padding: "12px",
          borderRadius: "6px",
          margin: "20px auto",
          maxWidth: "600px",
          border: "1px solid #f5c6cb"
        }}>
          {errorMessage}
          <button
            onClick={() => setErrorMessage(null)}
            style={{
              marginLeft: "10px",
              background: "none",
              border: "none",
              color: "#721c24",
              cursor: "pointer",
              fontSize: "16px"
            }}
          >
            ×
          </button>
        </div>
      )}

      {userRole && (
        <p>Your role: <strong>{userRole}</strong></p>
      )}

      <div style={{ marginTop: "30px" }}>
        <h3>Available Features:</h3>
        <div style={{ display: "flex", flexDirection: "column", gap: "10px", alignItems: "center" }}>
          {userRole && (
            <a
              href="/broadcast"
              style={{
                padding: "10px 20px",
                backgroundColor: "#007bff",
                color: "white",
                textDecoration: "none",
                borderRadius: "5px",
                display: "inline-block"
              }}
            >
              📢 {userRole === "admin" ? "Manage Broadcasts" : "View Broadcasts"}
            </a>
          )}

          {userRole === "farmer" && (
            <>
              <a
                href="/produce"
                style={{
                  padding: "10px 20px",
                  backgroundColor: "#28a745",
                  color: "white",
                  textDecoration: "none",
                  borderRadius: "5px",
                  display: "inline-block"
                }}
              >
                🌾 Manage My Produce
              </a>

              <a
                href="/produce-market"
                style={{
                  padding: "10px 20px",
                  backgroundColor: "#6b46c1",
                  color: "white",
                  textDecoration: "none",
                  borderRadius: "5px",
                  display: "inline-block",
                  fontWeight: "bold"
                }}
              >
                🛒 Browse Produce Market
              </a>

              <a
                href="/negotiations/history"
                style={{
                  padding: "10px 20px",
                  backgroundColor: "#dd6b20",
                  color: "white",
                  textDecoration: "none",
                  borderRadius: "5px",
                  display: "inline-block",
                  fontWeight: "bold"
                }}
              >
                💬 My Negotiations
              </a>
            </>
          )}

          {userRole === "buyer" && (
            <>
              <a
                href="/produce-market"
                style={{
                  padding: "10px 20px",
                  backgroundColor: "#6b46c1",
                  color: "white",
                  textDecoration: "none",
                  borderRadius: "5px",
                  display: "inline-block",
                  fontWeight: "bold"
                }}
              >
                🛒 Browse Produce Market
              </a>

              <a
                href="/negotiations/history"
                style={{
                  padding: "10px 20px",
                  backgroundColor: "#dd6b20",
                  color: "white",
                  textDecoration: "none",
                  borderRadius: "5px",
                  display: "inline-block",
                  fontWeight: "bold"
                }}
              >
                💬 My Negotiations
              </a>
            </>
          )}

          <a
            href="/test-register"
            style={{
              padding: "10px 20px",
              backgroundColor: "#28a745",
              color: "white",
              textDecoration: "none",
              borderRadius: "5px",
              display: "inline-block"
            }}
          >
            🧪 Test Registration
          </a>

          <a
            href="/cors-test"
            style={{
              padding: "10px 20px",
              backgroundColor: "#17a2b8",
              color: "white",
              textDecoration: "none",
              borderRadius: "5px",
              display: "inline-block"
            }}
          >
            🌐 Test CORS
          </a>

          <a
            href="/login-test"
            style={{
              padding: "10px 20px",
              backgroundColor: "#ffc107",
              color: "black",
              textDecoration: "none",
              borderRadius: "5px",
              display: "inline-block"
            }}
          >
            🔐 Test Login
          </a>

          <a
            href="/broadcast-test"
            style={{
              padding: "10px 20px",
              backgroundColor: "#6f42c1",
              color: "white",
              textDecoration: "none",
              borderRadius: "5px",
              display: "inline-block"
            }}
          >
            📡 Test Broadcast API
          </a>

          <a
            href="/login"
            style={{
              padding: "10px 20px",
              backgroundColor: "#6c757d",
              color: "white",
              textDecoration: "none",
              borderRadius: "5px",
              display: "inline-block"
            }}
          >
            🔓 Logout
          </a>
        </div>
      </div>
    </div>
  );
}
