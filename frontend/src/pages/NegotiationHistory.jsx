import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { negotiationAPI } from '../utils/api';
import './NegotiationHistory.css';

const NegotiationHistory = () => {
  const [negotiations, setNegotiations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentUser, setCurrentUser] = useState(null);
  const [filter, setFilter] = useState('all'); // all, pending, accepted, declined, rejected
  
  const navigate = useNavigate();

  useEffect(() => {
    // Get current user from local storage (authentication is handled by ProtectedRoute)
    const userInfo = localStorage.getItem('userInfo');
    if (userInfo) {
      try {
        setCurrentUser(JSON.parse(userInfo));
      } catch (err) {
        console.error('Error parsing user info:', err);
      }
    }

    fetchNegotiations();
  }, []);

  const fetchNegotiations = async () => {
    try {
      setLoading(true);
      const data = await negotiationAPI.getNegotiationHistory();
      setNegotiations(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch negotiation history. Please try again.');
      console.error('Error fetching negotiations:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleViewNegotiation = (produceId) => {
    navigate(`/negotiation/${produceId}`);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusClass = (status) => {
    switch (status) {
      case 'accepted': return 'status-accepted';
      case 'declined': return 'status-declined';
      case 'rejected': return 'status-rejected';
      default: return 'status-pending';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'accepted': return '✅';
      case 'declined': return '⚠️';
      case 'rejected': return '❌';
      default: return '⏳';
    }
  };

  // Filter negotiations based on selected filter
  const filteredNegotiations = negotiations.filter(negotiation => {
    if (filter === 'all') return true;
    return negotiation.status === filter;
  });

  if (loading) {
    return (
      <div className="negotiation-history">
        <div className="loading">Loading negotiation history...</div>
      </div>
    );
  }

  return (
    <div className="negotiation-history">
      <div className="history-header">
        <h1>💬 My Negotiations</h1>
        <p>Track all your ongoing and completed negotiations</p>
        <button onClick={() => navigate('/produce-market')} className="back-to-market-btn">
          🛒 Back to Market
        </button>
      </div>

      {error && (
        <div className="error-message">
          {error}
          <button onClick={fetchNegotiations} className="retry-btn">Retry</button>
        </div>
      )}

      <div className="history-filters">
        <div className="filter-buttons">
          <button
            className={`filter-btn ${filter === 'all' ? 'active' : ''}`}
            onClick={() => setFilter('all')}
          >
            All ({negotiations.length})
          </button>
          <button
            className={`filter-btn ${filter === 'pending' ? 'active' : ''}`}
            onClick={() => setFilter('pending')}
          >
            Pending ({negotiations.filter(n => n.status === 'pending').length})
          </button>
          <button
            className={`filter-btn ${filter === 'accepted' ? 'active' : ''}`}
            onClick={() => setFilter('accepted')}
          >
            Accepted ({negotiations.filter(n => n.status === 'accepted').length})
          </button>
          <button
            className={`filter-btn ${filter === 'declined' ? 'active' : ''}`}
            onClick={() => setFilter('declined')}
          >
            Declined ({negotiations.filter(n => n.status === 'declined').length})
          </button>
          <button
            className={`filter-btn ${filter === 'rejected' ? 'active' : ''}`}
            onClick={() => setFilter('rejected')}
          >
            Rejected ({negotiations.filter(n => n.status === 'rejected').length})
          </button>
        </div>
      </div>

      {filteredNegotiations.length === 0 ? (
        <div className="no-negotiations">
          <h3>No negotiations found</h3>
          <p>
            {filter === 'all' 
              ? "You haven't started any negotiations yet." 
              : `No ${filter} negotiations found.`
            }
          </p>
          <button onClick={() => navigate('/produce-market')} className="start-negotiating-btn">
            Start Negotiating
          </button>
        </div>
      ) : (
        <div className="negotiations-list">
          {filteredNegotiations.map((negotiation) => (
            <div key={negotiation.negotiation_id} className="negotiation-card">
              <div className="negotiation-header">
                <div className="negotiation-title">
                  <h3>{negotiation.crop}</h3>
                  <span className="category-badge">{negotiation.category}</span>
                </div>
                <div className={`status-badge ${getStatusClass(negotiation.status)}`}>
                  {getStatusIcon(negotiation.status)} {negotiation.status.toUpperCase()}
                </div>
              </div>

              <div className="negotiation-details">
                <div className="detail-row">
                  <span className="label">Negotiation ID:</span>
                  <span className="value">{negotiation.negotiation_id}</span>
                </div>
                <div className="detail-row">
                  <span className="label">Produce ID:</span>
                  <span className="value">{negotiation.produce_id}</span>
                </div>
                <div className="detail-row">
                  <span className="label">Farmer:</span>
                  <span className="value">{negotiation.farmer_name}</span>
                </div>
                {negotiation.buyer_name && (
                  <div className="detail-row">
                    <span className="label">Buyer:</span>
                    <span className="value">{negotiation.buyer_name}</span>
                  </div>
                )}
                <div className="detail-row">
                  <span className="label">Started On:</span>
                  <span className="value">{formatDate(negotiation.started_on)}</span>
                </div>
                <div className="detail-row">
                  <span className="label">Last Activity:</span>
                  <span className="value">{formatDate(negotiation.last_activity)}</span>
                </div>
                <div className="detail-row">
                  <span className="label">Messages:</span>
                  <span className="value">{negotiation.message_count}</span>
                </div>
              </div>

              <div className="pricing-info">
                <div className="price-row">
                  <span className="label">Original Price:</span>
                  <span className="original-price">₹{negotiation.original_price}</span>
                </div>
                {negotiation.current_offer_price && (
                  <div className="price-row">
                    <span className="label">Current Offer:</span>
                    <span className="offer-price">₹{negotiation.current_offer_price}</span>
                  </div>
                )}
                {negotiation.offered_quantity && (
                  <div className="price-row">
                    <span className="label">Quantity:</span>
                    <span className="quantity">{negotiation.offered_quantity} units</span>
                  </div>
                )}
                {negotiation.delivery_location && (
                  <div className="price-row">
                    <span className="label">Delivery:</span>
                    <span className="delivery">{negotiation.delivery_location}</span>
                  </div>
                )}
              </div>

              <div className="negotiation-actions">
                <button
                  onClick={() => handleViewNegotiation(negotiation.produce_id)}
                  className="view-negotiation-btn"
                >
                  💬 View Conversation
                </button>
                {negotiation.status === 'pending' && (
                  <span className="pending-indicator">Awaiting response...</span>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default NegotiationHistory;
