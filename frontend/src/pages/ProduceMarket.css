.produce-market {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.market-header {
  text-align: center;
  margin-bottom: 30px;
}

.market-header h1 {
  color: #2c3e50;
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.market-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

.loading {
  text-align: center;
  padding: 50px;
  font-size: 1.2rem;
  color: #7f8c8d;
}

.error-message {
  background-color: #fee;
  border: 1px solid #fcc;
  color: #c33;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.retry-btn {
  background-color: #e74c3c;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.retry-btn:hover {
  background-color: #c0392b;
}

.market-filters {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.search-box {
  margin-bottom: 15px;
}

.search-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.filter-controls {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.category-filter,
.sort-filter {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  min-width: 150px;
}

.market-stats {
  margin-bottom: 20px;
  color: #7f8c8d;
  font-size: 14px;
}

.produces-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.produce-card {
  background-color: white;
  border: 1px solid #e1e8ed;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.produce-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.produce-image {
  height: 200px;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.produce-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-image {
  font-size: 3rem;
  color: #bdc3c7;
}

.produce-details {
  padding: 20px;
}

.produce-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.produce-category,
.produce-grade {
  color: #7f8c8d;
  font-size: 14px;
  margin-bottom: 5px;
}

.produce-pricing {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 15px 0;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.price {
  font-size: 1.3rem;
  font-weight: 600;
  color: #27ae60;
}

.quantity {
  color: #7f8c8d;
  font-size: 14px;
}

.produce-location {
  color: #7f8c8d;
  font-size: 14px;
  margin-bottom: 10px;
}

.produce-description {
  color: #5a6c7d;
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 15px;
}

.produce-farmer {
  color: #7f8c8d;
  font-size: 14px;
  margin-bottom: 15px;
  font-weight: 500;
}

.produce-actions {
  display: flex;
  gap: 10px;
}

.conversation-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  flex: 1;
}

.conversation-btn:hover {
  background-color: #2980b9;
}

.no-produces {
  text-align: center;
  padding: 60px 20px;
  color: #7f8c8d;
}

.no-produces h3 {
  margin-bottom: 10px;
  color: #5a6c7d;
}

/* Responsive Design */
@media (max-width: 768px) {
  .produce-market {
    padding: 15px;
  }
  
  .market-header h1 {
    font-size: 2rem;
  }
  
  .produces-grid {
    grid-template-columns: 1fr;
  }
  
  .filter-controls {
    flex-direction: column;
  }
  
  .category-filter,
  .sort-filter {
    min-width: 100%;
  }
}

@media (max-width: 480px) {
  .produce-pricing {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .produce-card {
    margin-bottom: 15px;
  }
}
