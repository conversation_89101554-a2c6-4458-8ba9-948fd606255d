import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { negotiationAPI, produceAPI } from '../utils/api';
import NegotiationAssistant from '../components/NegotiationAssistant';
import './NegotiationChat.css';

const NegotiationChat = () => {
  const { produceId } = useParams();
  const navigate = useNavigate();
  const messagesEndRef = useRef(null);
  
  const [negotiations, setNegotiations] = useState([]);
  const [produce, setProduce] = useState(null);
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Message form state
  const [messageInput, setMessageInput] = useState('');
  const [offerType, setOfferType] = useState('propose');
  const [offeredPrice, setOfferedPrice] = useState('');
  const [offeredQuantity, setOfferedQuantity] = useState('');
  const [deliveryLocation, setDeliveryLocation] = useState('');
  const [sending, setSending] = useState(false);

  useEffect(() => {
    // Get current user from local storage (authentication is handled by ProtectedRoute)
    const userInfo = localStorage.getItem('userInfo');
    if (userInfo) {
      try {
        setCurrentUser(JSON.parse(userInfo));
      } catch (err) {
        console.error('Error parsing user info:', err);
      }
    }

    fetchData();
  }, [produceId]);

  useEffect(() => {
    scrollToBottom();
  }, [negotiations]);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      // Fetch produce details
      const produceData = await produceAPI.getProduceById(produceId);
      setProduce(produceData);
      
      // Fetch negotiations for this produce
      const negotiationsData = await negotiationAPI.getNegotiationsWithDetails(produceId);
      setNegotiations(negotiationsData);
      
      setError(null);
    } catch (err) {
      setError('Failed to load negotiation data. Please try again.');
      console.error('Error fetching data:', err);
    } finally {
      setLoading(false);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async (e) => {
    e.preventDefault();
    
    if (!messageInput.trim()) {
      return;
    }

    try {
      setSending(true);
      
      const negotiationData = {
        produce_id: parseInt(produceId),
        message: messageInput,
        type: offerType,
        offered_price: offeredPrice ? parseFloat(offeredPrice) : null,
        offered_quantity: offeredQuantity ? parseFloat(offeredQuantity) : null,
        delivery_location: deliveryLocation || null
      };

      await negotiationAPI.createNegotiation(negotiationData);
      
      // Clear form
      setMessageInput('');
      setOfferedPrice('');
      setOfferedQuantity('');
      setDeliveryLocation('');
      setOfferType('propose');
      
      // Refresh negotiations
      await fetchData();
      
    } catch (err) {
      setError('Failed to send message. Please try again.');
      console.error('Error sending message:', err);
    } finally {
      setSending(false);
    }
  };

  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getMessageTypeLabel = (type) => {
    switch (type) {
      case 'propose': return 'Propose';
      case 'accept': return 'Accept';
      case 'decline': return 'Decline';
      case 'reject': return 'Reject';
      default: return type;
    }
  };

  const getMessageTypeClass = (type) => {
    switch (type) {
      case 'accept': return 'message-accept';
      case 'decline': return 'message-decline';
      case 'reject': return 'message-reject';
      default: return 'message-propose';
    }
  };

  if (loading) {
    return (
      <div className="negotiation-chat">
        <div className="loading">Loading negotiation...</div>
      </div>
    );
  }

  if (!produce) {
    return (
      <div className="negotiation-chat">
        <div className="error">Produce not found</div>
      </div>
    );
  }

  return (
    <div className="negotiation-chat">
      {/* Header with produce info */}
      <div className="chat-header">
        <button onClick={() => navigate('/produce-market')} className="back-btn">
          ← Back to Market
        </button>
        <div className="produce-info">
          <h2>{produce.crop} Grade {produce.grade}</h2>
          <p>Location: {produce.location} | ₹{produce.price_per_unit}/{produce.unit} | {produce.quantity} {produce.unit} available</p>
        </div>
      </div>

      <div className="chat-container">
        {/* Negotiation Summary Sidebar */}
        <div className="negotiation-summary">
          <h3>Summary Info Box</h3>
          <div className="summary-details">
            <div className="summary-row">
              <span>Negotiation ID</span>
              <span>{negotiations.length > 0 ? negotiations[0].id : 'N/A'}</span>
            </div>
            <div className="summary-row">
              <span>Produce ID</span>
              <span>{produce.id}</span>
            </div>
            <div className="summary-row">
              <span>Farmer</span>
              <span>{produce.farmer_name || 'Unknown'}</span>
            </div>
            <div className="summary-row">
              <span>Buyer</span>
              <span>{currentUser?.role === 'buyer' ? currentUser.name : 'You'}</span>
            </div>
            <div className="summary-row">
              <span>Started On</span>
              <span>{negotiations.length > 0 ? formatDateTime(negotiations[0].created_at).split(' ')[0] : 'N/A'}</span>
            </div>
            <div className="summary-row">
              <span>Status</span>
              <span className="status-accepted">
                {negotiations.length > 0 ? getMessageTypeLabel(negotiations[negotiations.length - 1].type) : 'Pending'}
              </span>
            </div>
          </div>
        </div>

        {/* Chat Messages */}
        <div className="chat-messages">
          <div className="messages-container">
            {error && (
              <div className="error-message">
                {error}
                <button onClick={fetchData} className="retry-btn">Retry</button>
              </div>
            )}
            
            {negotiations.length === 0 ? (
              <div className="no-messages">
                <p>No messages yet. Start the conversation!</p>
              </div>
            ) : (
              negotiations.map((negotiation) => (
                <div
                  key={negotiation.id}
                  className={`message ${negotiation.sender_id === currentUser?.user_id ? 'message-own' : 'message-other'} ${getMessageTypeClass(negotiation.type)}`}
                >
                  <div className="message-header">
                    <span className="sender-name">
                      {negotiation.sender_id === currentUser?.user_id ? 'You' : negotiation.sender_name}
                      {negotiation.sender_role === 'farmer' ? ' (Farmer)' : ' (Buyer)'}
                    </span>
                    <span className="message-time">{formatDateTime(negotiation.created_at)}</span>
                  </div>
                  <div className="message-type">
                    Type: {getMessageTypeLabel(negotiation.type)}
                  </div>
                  <div className="message-content">
                    {negotiation.message}
                  </div>
                  {(negotiation.offered_price || negotiation.offered_quantity || negotiation.delivery_location) && (
                    <div className="message-offer-details">
                      {negotiation.offered_price && (
                        <div>Offered Price: ₹{negotiation.offered_price}/{produce.unit}</div>
                      )}
                      {negotiation.offered_quantity && (
                        <div>Quantity: {negotiation.offered_quantity} {produce.unit}</div>
                      )}
                      {negotiation.delivery_location && (
                        <div>Delivery: {negotiation.delivery_location}</div>
                      )}
                    </div>
                  )}
                </div>
              ))
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Message Input Form */}
          <form onSubmit={handleSendMessage} className="message-form">
            <div className="message-input-container">
              <textarea
                value={messageInput}
                onChange={(e) => setMessageInput(e.target.value)}
                placeholder="Type a message..."
                className="message-input"
                rows="3"
                disabled={sending}
              />
            </div>
            
            <div className="offer-details">
              <div className="offer-row">
                <label>Offer Type:</label>
                <select
                  value={offerType}
                  onChange={(e) => setOfferType(e.target.value)}
                  className="offer-type-select"
                  disabled={sending}
                >
                  <option value="propose">Propose</option>
                  <option value="accept">Accept</option>
                  <option value="decline">Decline</option>
                  <option value="reject">Reject</option>
                </select>
              </div>
              
              {offerType === 'propose' && (
                <>
                  <div className="offer-row">
                    <label>Price (₹/{produce.unit}):</label>
                    <input
                      type="number"
                      step="0.01"
                      value={offeredPrice}
                      onChange={(e) => setOfferedPrice(e.target.value)}
                      className="offer-input"
                      placeholder="Enter price"
                      disabled={sending}
                    />
                  </div>
                  
                  <div className="offer-row">
                    <label>Quantity ({produce.unit}):</label>
                    <input
                      type="number"
                      step="0.01"
                      value={offeredQuantity}
                      onChange={(e) => setOfferedQuantity(e.target.value)}
                      className="offer-input"
                      placeholder="Enter quantity"
                      disabled={sending}
                    />
                  </div>
                  
                  <div className="offer-row">
                    <label>Delivery Location:</label>
                    <input
                      type="text"
                      value={deliveryLocation}
                      onChange={(e) => setDeliveryLocation(e.target.value)}
                      className="offer-input"
                      placeholder="Enter delivery location"
                      disabled={sending}
                    />
                  </div>
                </>
              )}
            </div>
            
            <button type="submit" className="send-btn" disabled={sending || !messageInput.trim()}>
              {sending ? 'Sending...' : 'Send'}
            </button>
          </form>

          {/* Negotiation Assistant */}
          {currentUser && (
            <NegotiationAssistant
              produceId={produceId}
              role={currentUser.role}
              history={negotiations.map(n => n.message)}
              setMessageInput={setMessageInput}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default NegotiationChat;
