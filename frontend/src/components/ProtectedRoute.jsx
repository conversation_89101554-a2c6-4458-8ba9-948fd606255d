import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

const ProtectedRoute = ({ children, allowedRoles = [] }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAuth = () => {
      const token = localStorage.getItem('token');
      const userInfo = localStorage.getItem('userInfo');

      // Check if user is authenticated
      if (!token || !userInfo) {
        navigate('/login', { 
          state: { 
            returnUrl: location.pathname + location.search,
            message: 'Please login to access this page.'
          } 
        });
        return;
      }

      try {
        const user = JSON.parse(userInfo);
        
        // Check if user role is allowed
        if (allowedRoles.length > 0 && !allowedRoles.includes(user.role)) {
          navigate('/', { 
            state: { 
              error: `Access denied. This page is only accessible to ${allowedRoles.join(' and ')}s.` 
            } 
          });
          return;
        }

        // Verify token is not expired (basic check)
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          const currentTime = Date.now() / 1000;
          
          if (payload.exp && payload.exp < currentTime) {
            localStorage.removeItem('token');
            localStorage.removeItem('userInfo');
            navigate('/login', { 
              state: { 
                returnUrl: location.pathname + location.search,
                message: 'Your session has expired. Please login again.'
              } 
            });
            return;
          }
        } catch (tokenError) {
          console.error('Error parsing token:', tokenError);
          localStorage.removeItem('token');
          localStorage.removeItem('userInfo');
          navigate('/login', { 
            state: { 
              returnUrl: location.pathname + location.search,
              message: 'Invalid session. Please login again.'
            } 
          });
          return;
        }

        setIsAuthorized(true);
      } catch (err) {
        console.error('Error parsing user info:', err);
        localStorage.removeItem('token');
        localStorage.removeItem('userInfo');
        navigate('/login', { 
          state: { 
            returnUrl: location.pathname + location.search,
            message: 'Invalid user data. Please login again.'
          } 
        });
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [navigate, location, allowedRoles]);

  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '1.2rem',
        color: '#666'
      }}>
        Checking authentication...
      </div>
    );
  }

  if (!isAuthorized) {
    return null; // Navigation will handle redirect
  }

  return children;
};

export default ProtectedRoute;
