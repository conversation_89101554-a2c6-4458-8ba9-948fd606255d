import axios from "axios";

const API_BASE_URL = "http://localhost:8000";

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  withCredentials: true, // Enable credentials for CORS
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Broadcast API functions
export const broadcastAPI = {
  // Send a broadcast message (admin only)
  sendBroadcast: async (message) => {
    try {
      const response = await api.post("/broadcast/", { message });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Get all broadcast messages (accessible to all authenticated users)
  getBroadcasts: async () => {
    try {
      const response = await api.get("/broadcast/");
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Get a specific broadcast by ID
  getBroadcast: async (broadcastId) => {
    try {
      const response = await api.get(`/broadcast/${broadcastId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Update a broadcast message (admin only)
  updateBroadcast: async (broadcastId, message) => {
    try {
      const response = await api.put(`/broadcast/${broadcastId}`, { message });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Delete a broadcast message (admin only)
  deleteBroadcast: async (broadcastId) => {
    try {
      const response = await api.delete(`/broadcast/${broadcastId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
};

// Produce API functions
export const produceAPI = {
  // Create a new produce listing (farmers only)
  createProduce: async (produceData) => {
    try {
      const response = await api.post("/produce/", produceData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Get all produce listings
  getAllProduce: async () => {
    try {
      const response = await api.get("/produce/");
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Get farmer's own produce listings
  getMyProduce: async () => {
    try {
      const response = await api.get("/produce/farmer/my-produce");
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Get produce by ID
  getProduceById: async (produceId) => {
    try {
      const response = await api.get(`/produce/${produceId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Get specific produce by ID
  getProduce: async (produceId) => {
    try {
      const response = await api.get(`/produce/${produceId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Update a produce listing (farmers only - own produce)
  updateProduce: async (produceId, produceData) => {
    try {
      const response = await api.put(`/produce/${produceId}`, produceData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Delete a produce listing (farmers only - own produce)
  deleteProduce: async (produceId) => {
    try {
      const response = await api.delete(`/produce/${produceId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
};

// Auth API functions
export const authAPI = {
  login: async (formData) => {
    const response = await axios.post(`${API_BASE_URL}/users/login`, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
    return response.data;
  },

  register: async (formData) => {
    console.log("API: Sending registration data:", formData);
    try {
      const response = await axios.post(`${API_BASE_URL}/users/register`, formData, {
        headers: {
          'Content-Type': 'application/json',
        },
      });
      console.log("API: Registration successful:", response.data);
      return response.data;
    } catch (error) {
      console.error("API: Registration failed:", error.response?.data || error.message);
      throw error;
    }
  },
};

// Negotiation API functions
export const negotiationAPI = {
  // Create a new negotiation
  createNegotiation: async (negotiationData) => {
    try {
      const response = await api.post("/negotiations/", negotiationData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Get negotiations for a specific produce
  getNegotiationsByProduce: async (produceId) => {
    try {
      const response = await api.get(`/negotiations/produce/${produceId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Get negotiations with detailed information for a specific produce
  getNegotiationsWithDetails: async (produceId) => {
    try {
      const response = await api.get(`/negotiations/produce/${produceId}/details`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Get user's negotiation history
  getNegotiationHistory: async () => {
    try {
      const response = await api.get("/negotiations/history");
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Get negotiation summary by ID
  getNegotiationSummary: async (negotiationId) => {
    try {
      const response = await api.get(`/negotiations/summary/${negotiationId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
};

export default api;
