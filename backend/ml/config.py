# Description: Centralized config paths for ML pipeline
# File: backend/ml/config.py

# import libraries
import os

#BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))+'/ml'
# RAW_DATA_PATH = os.path.join(BASE_DIR, "datasets", "ml_training_data.csv")
# ENHANCED_DATA_PATH = os.path.join(BASE_DIR, "datasets", "ml_training_data_enhanced.csv")
# MODEL_PATH = os.path.join(BASE_DIR, "models", "latest_model.pkl")
# INTENT_MODEL_PATH = os.path.join(BASE_DIR, "models", "negotiation_classifier.pkl")
# LOG_PATH = os.path.join(BASE_DIR, "logs", "train_log.json")
# LOG_HISTORY_PATH = os.path.join(BASE_DIR, "logs", "train_log_history.jsonl")
# REPORT_PATH = os.path.join(BASE_DIR, "reports", "shap_summary.png")

RAW_DATA_PATH = "datasets/ml_training_data.csv"
ENHANCED_DATA_PATH = "datasets/ml_training_data_enhanced.csv"
MODEL_PATH = "models/latest_model.pkl"
INTENT_MODEL_PATH = "models/negotiation_classifier.pkl"
LOG_PATH = "logs/train_log.json"
LOG_HISTORY_PATH = "logs/train_log_history.jsonl"
REPORT_PATH = "reports/shap_summary.png"

def ensure_directories():
    #os.makedirs(BASE_DIR, exist_ok=True)
    os.makedirs(os.path.dirname(RAW_DATA_PATH), exist_ok=True)
    os.makedirs(os.path.dirname(ENHANCED_DATA_PATH), exist_ok=True)
    os.makedirs(os.path.dirname(MODEL_PATH), exist_ok=True)
    os.makedirs(os.path.dirname(LOG_PATH), exist_ok=True)
    os.makedirs(os.path.dirname(LOG_HISTORY_PATH), exist_ok=True)
    os.makedirs(os.path.dirname(REPORT_PATH), exist_ok=True)