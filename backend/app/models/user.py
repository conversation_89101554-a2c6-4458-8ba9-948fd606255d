# Description: This module defines the User model for the application, which represents users in the system.
# File: backend/app/models/user.py

# import necessary libraries
from sqlalchemy import Column, Integer, Text, CheckConstraint, DateTime, func
from sqlalchemy.orm import relationship
from app.db.session import Base

# Define the User model class
class User(Base):
    __tablename__ = "users"

    user_id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(Text, nullable=False)
    email = Column(Text, unique=True, nullable=True)
    password_hash = Column(Text, nullable=False)
    role = Column(Text, nullable=False)
    location = Column(Text)
    phone_number = Column(Text, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    __table_args__ = (
        CheckConstraint("role IN ('farmer', 'buyer' ,'admin')", name="check_role_valid"),
        #CheckConstraint("role IN ('farmer', 'buyer')", name="check_role_valid"),
    )

    # Relationships
    produces = relationship("Produce", back_populates="farmer")