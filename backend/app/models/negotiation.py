# File: backend/app/models/negotiation.py
# Description: This module defines the Negotiation model for handling negotiations in the application.

# import necessary modules and classes
from sqlalchemy import Column, Integer, String, ForeignKey, Enum, Text, DateTime, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.database import Base
import enum

# Define class for OfferType enumeration
class OfferType(str, enum.Enum):
    propose = "propose"
    accept = "accept"
    decline = "decline"
    reject = "reject"

# Define class for SQLAlchemy model for negotiations
class Negotiation(Base):
    __tablename__ = "negotiations"
    id = Column(Integer, primary_key=True, index=True)
    produce_id = Column(Integer, ForeignKey("produces.id"))
    sender_id = Column(Integer, ForeignKey("users.user_id"))
    sender_role = Column(String, nullable=False)
    message = Column(Text, nullable=False)
    type = Column(Enum(OfferType), nullable=False)
    offered_price = Column(Float, nullable=True)
    offered_quantity = Column(Float, nullable=True)
    delivery_location = Column(String, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    produce = relationship("Produce")
    sender = relationship("User")