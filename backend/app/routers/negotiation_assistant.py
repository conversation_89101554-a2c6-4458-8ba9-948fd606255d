# Description: FastAPI router for handling negotiation suggestions using NLP.
# File: negotiation_assistant.py

# import necessary modules
from fastapi import APIRouter, Depends
from pydantic import BaseModel
from app.services.nlp_response_generator import generate_suggestions
from app.schemas.user import UserOut  # your JWT-decoded user model

router = APIRouter()

class SuggestionInput(BaseModel):
    produce_id: int
    history: list[str]  # previous negotiation messages

@router.post("/negotiations/suggest/")
#async def suggest_response(data: SuggestionInput, user: UserOut = Depends(...)):
async def suggest_response(data: SuggestionInput, user: UserOut = ''):
    suggestions = generate_suggestions(data.history, user.role)
    return {"suggestions": suggestions}
