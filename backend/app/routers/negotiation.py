# Description: API endpoints for negotiation-related operations.
# File: backend/app/routers/negotiation.py

# import necessary modules and classes
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.db.session import get_db
from app.services import auth as auth_service
from app.services import negotiation as negotiation_service
from app.schemas.negotiation import NegotiationCreate, NegotiationOut, NegotiationSummary, NegotiationWithDetails
from fastapi.security import OAuth2<PERSON>asswordBearer
from typing import List

router = APIRouter(prefix="/negotiations", tags=["Negotiation"])

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/users/login")

# Function to post a new offer for negotiation
@router.post("/", response_model=NegotiationOut)
def post_offer(offer: NegotiationCreate, token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    payload = auth_service.decode_access_token(token)
    if not payload:
        raise HTTPException(status_code=401, detail="Invalid token")
    return negotiation_service.create_offer(db, sender_id=int(payload["sub"]), sender_role=payload["role"], offer_data=offer)

# Function to get all offers for a specific produce
@router.get("/produce/{produce_id}", response_model=List[NegotiationOut])
def get_offers(produce_id: int, db: Session = Depends(get_db)):
    return negotiation_service.get_offers_by_produce(db, produce_id)

# Function to get negotiations with detailed information for a specific produce
@router.get("/produce/{produce_id}/details", response_model=List[NegotiationWithDetails])
def get_negotiations_with_details(produce_id: int, token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    payload = auth_service.decode_access_token(token)
    if not payload:
        raise HTTPException(status_code=401, detail="Invalid token")

    negotiations = negotiation_service.get_negotiations_with_details(db, produce_id)

    # Transform to include detailed information
    detailed_negotiations = []
    for neg in negotiations:
        detailed_neg = NegotiationWithDetails(
            id=neg.id,
            produce_id=neg.produce_id,
            sender_id=neg.sender_id,
            sender_role=neg.sender_role,
            sender_name=neg.sender.name,
            message=neg.message,
            type=neg.type,
            offered_price=neg.offered_price,
            offered_quantity=neg.offered_quantity,
            delivery_location=neg.delivery_location,
            created_at=neg.created_at,
            crop=neg.produce.crop,
            category=neg.produce.category,
            original_price=neg.produce.price_per_unit,
            unit=neg.produce.unit,
            available_quantity=neg.produce.quantity,
            farmer_name=neg.produce.farmer.name if neg.produce.farmer else "Unknown",
            produce_location=neg.produce.location
        )
        detailed_negotiations.append(detailed_neg)

    return detailed_negotiations

# Function to get user's negotiation history/summaries
@router.get("/history", response_model=List[NegotiationSummary])
def get_user_negotiations(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    payload = auth_service.decode_access_token(token)
    if not payload:
        raise HTTPException(status_code=401, detail="Invalid token")

    user_id = int(payload["sub"])
    user_role = payload["role"]

    return negotiation_service.get_negotiation_summaries_by_user(db, user_id, user_role)

# Function to get negotiation summary by ID
@router.get("/summary/{negotiation_id}", response_model=NegotiationSummary)
def get_negotiation_summary(negotiation_id: int, token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    payload = auth_service.decode_access_token(token)
    if not payload:
        raise HTTPException(status_code=401, detail="Invalid token")

    summary = negotiation_service.get_negotiation_summary_by_id(db, negotiation_id)
    if not summary:
        raise HTTPException(status_code=404, detail="Negotiation not found")

    return summary
