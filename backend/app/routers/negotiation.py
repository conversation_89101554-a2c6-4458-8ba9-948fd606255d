# Description: API endpoints for negotiation-related operations.
# File: backend/app/routers/negotiation.py

# import necessary modules and classes
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.db.session import get_db
from app.services import auth as auth_service
from app.services import negotiation as negotiation_service
from app.schemas.negotiation import NegotiationCreate, NegotiationOut
from fastapi.security import OAuth2PasswordBearer
from typing import List

router = APIRouter(prefix="/negotiations", tags=["Negotiation"])

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/users/login")

# Function to post a new offer for negotiation
@router.post("/", response_model=NegotiationOut)
def post_offer(offer: NegotiationCreate, token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    payload = auth_service.decode_access_token(token)
    if not payload:
        raise HTTPException(status_code=401, detail="Invalid token")
    return negotiation_service.create_offer(db, sender_id=int(payload["sub"]), sender_role=payload["role"], offer_data=offer)

# Function to get all offers for a specific produce
@router.get("/{produce_id}", response_model=List[NegotiationOut])
def get_offers(produce_id: int, db: Session = Depends(get_db)):
    return negotiation_service.get_offers_by_produce(db, produce_id)

# Function to get all negotiations for the current user
@router.get("/user/history", response_model=List[NegotiationOut])
def get_user_negotiations(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    payload = auth_service.decode_access_token(token)
    if not payload:
        raise HTTPException(status_code=401, detail="Invalid token")

    user_id = int(payload["sub"])
    return negotiation_service.get_negotiations_by_user(db, user_id)
