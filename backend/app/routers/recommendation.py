# Description: FastAPI router for handling produce recommendations for buyers.
# File: backend/app/routers/recommendation.py

# import necessary libraries
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from fastapi.security import OAuth2PasswordBearer
from typing import List, Optional
from app.db.session import get_db
from app.services import recommendation as recommendation_service
from app.services import auth as auth_service
from app.schemas.produce import ProduceOut
#from ml.predict import predict_top_produce_for_user

router = APIRouter(prefix="/recommendations", tags=["Recommendations"])
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/users/login")

# Function to get the buyer's user ID from the token
def get_buyer_user_id(token: str = Depends(oauth2_scheme)) -> int:
    payload = auth_service.decode_access_token(token)
    if not payload:
        raise HTTPException(status_code=401, detail="Invalid token")
    if payload.get("role") != "buyer":
        raise HTTPException(status_code=403, detail="Only buyers can access recommendations")
    return int(payload["sub"])

# Function to recommend produce based on content filtering
@router.get("/produce", response_model=List[ProduceOut])
def recommend_produce_content_based(
    user_id: int = Depends(get_buyer_user_id),
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 10,
    category: Optional[str] = Query(None),
    location: Optional[str] = Query(None),
    status: Optional[str] = Query("available")
):
    return recommendation_service.get_recommendations_for_buyer(
        db=db,
        buyer_id=user_id,
        skip=skip,
        limit=limit,
        category=category,
        location=location,
        status=status
    )

# General Recommendation Strategy (could be hybrid) - this method provides a general recommendation based on user preferences and produce availability
@router.get("/general", response_model=List[ProduceOut])
def recommend_general(
    user_id: int = Depends(get_buyer_user_id),
    db: Session = Depends(get_db)
):
    return recommendation_service.get_general_recommendations(
        db=db,
        buyer_id=user_id
    )

# Function to recommend produce using collaborative filtering
@router.get("/collaborative", response_model=List[ProduceOut])
def recommend_collaborative(
    user_id: int = Depends(get_buyer_user_id),
    db: Session = Depends(get_db)
):
    return recommendation_service.get_collab_recommendations(
        db=db,
        buyer_id=user_id
    )

# Function to recommend produce using hybrid approach
@router.get("/hybrid", response_model=List[ProduceOut])
def recommend_hybrid(
    user_id: int = Depends(get_buyer_user_id),
    db: Session = Depends(get_db)
):
    return recommendation_service.get_hybrid_recommendations(
        db=db,
        buyer_id=user_id
    )       

# Function to recommend trending produce
@router.get("/trending", response_model=List[ProduceOut])
def recommend_trending(
    db: Session = Depends(get_db),
    limit: int = 10
):
    return recommendation_service.get_trending_produce(
        db=db,
        limit=limit
    )

# Function to recommend produce based on feedback
@router.get("/feedback", response_model=List[ProduceOut])
def recommend_feedback_based(
    user_id: int = Depends(get_buyer_user_id),
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 10
):
    return recommendation_service.get_feedback_based_recommendations(
        db=db,
        buyer_id=user_id,
        skip=skip,
        limit=limit
    )

# Function to recommend produce based on negotiation history
@router.get("/negotiation", response_model=List[ProduceOut])
def recommend_negotiation_based(
    user_id: int = Depends(get_buyer_user_id),
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 10
):
    return recommendation_service.get_negotiation_based_recommendations(
        db=db,
        buyer_id=user_id,
        skip=skip,
        limit=limit
    )

# Function to recommend produce based on user preferences
@router.get("/preferences", response_model=List[ProduceOut])
def recommend_preferences_based(
    user_id: int = Depends(get_buyer_user_id),
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 10
):
    return recommendation_service.get_preferences_based_recommendations(
        db=db,
        buyer_id=user_id,
        skip=skip,
        limit=limit
    )

# Function to recommend produce based on location
@router.get("/location", response_model=List[ProduceOut])
def recommend_location_based(
    user_id: int = Depends(get_buyer_user_id),
    db: Session = Depends(get_db),
    location: str = Query(...),
    skip: int = 0,
    limit: int = 10
):
    return recommendation_service.get_location_based_recommendations(
        db=db,
        buyer_id=user_id,
        location=location,
        skip=skip,
        limit=limit
    )

# Function to recommend produce based on price range
@router.get("/price", response_model=List[ProduceOut])
def recommend_price_based(
    user_id: int = Depends(get_buyer_user_id),
    db: Session = Depends(get_db),
    min_price: float = Query(0.0),
    max_price: float = Query(1e10),
    skip: int = 0,
    limit: int = 10
):
    return recommendation_service.get_price_based_recommendations(
        db=db,
        buyer_id=user_id,
        min_price=min_price,
        max_price=max_price,
        skip=skip,
        limit=limit
    )

# Machine Learning Based Recommendations - Uses pre-trained model to predict engagement scores
@router.get("/recommendations/ml", response_model=List[ProduceOut])
def recommend_ml_based(
    user_id: int = Depends(get_buyer_user_id),
    db: Session = Depends(get_db),
    top_n: int = 5
):
    return predict_top_produce_for_user(db=db, buyer_id=user_id, top_n=top_n)
