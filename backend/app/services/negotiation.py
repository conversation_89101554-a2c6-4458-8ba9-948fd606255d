#   Description: Service layer for handling negotiation-related operations.
# File: backend/app/services/negotiation.py

# import necessary modules and classses
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, distinct, desc
from app.models.negotiation import Negotiation
from app.models.produce import Produce
from app.models.user import User
from app.schemas.negotiation import NegotiationCreate, NegotiationSummary, NegotiationStatus
from typing import List, Optional

# Function to create a new negotiation offer
def create_offer(db: Session, sender_id: int, sender_role: str, offer_data: NegotiationCreate):
    negotiation = Negotiation(
        produce_id=offer_data.produce_id,
        sender_id=sender_id,
        sender_role=sender_role,
        message=offer_data.message,
        type=offer_data.type,
        offered_price=offer_data.offered_price,
        offered_quantity=offer_data.offered_quantity,
        delivery_location=offer_data.delivery_location,
    )
    db.add(negotiation)
    db.commit()
    db.refresh(negotiation)
    return negotiation

# Function to get all offers for a specific produce
def get_offers_by_produce(db: Session, produce_id: int):
    return db.query(Negotiation).filter(Negotiation.produce_id == produce_id).order_by(Negotiation.created_at.asc()).all()

# Function to get negotiations with detailed information
def get_negotiations_with_details(db: Session, produce_id: int):
    return db.query(Negotiation)\
        .options(joinedload(Negotiation.sender), joinedload(Negotiation.produce))\
        .filter(Negotiation.produce_id == produce_id)\
        .order_by(Negotiation.created_at.asc())\
        .all()

# Function to get all negotiations for a specific user
def get_negotiations_by_user(db: Session, user_id: int):
    # Get distinct produce IDs where the user is involved in negotiations
    # This includes both negotiations sent by the user and negotiations about the user's produce
    produce_ids = db.query(distinct(Negotiation.produce_id)).filter(
        Negotiation.sender_id == user_id
    ).all()

    # Flatten the list of tuples into a simple list of produce IDs
    produce_ids = [pid[0] for pid in produce_ids]

    result = []
    # For each produce, get all related negotiations
    for pid in produce_ids:
        negotiations = get_offers_by_produce(db, pid)
        if negotiations:
            result.extend(negotiations)

    return result

# Function to get negotiation summaries for a user
def get_negotiation_summaries_by_user(db: Session, user_id: int, user_role: str) -> List[NegotiationSummary]:
    # Get negotiations where user is involved (either as sender or as produce owner)
    if user_role == "farmer":
        # For farmers, get negotiations on their produce
        negotiations_query = db.query(
            Negotiation.produce_id,
            func.min(Negotiation.created_at).label('started_on'),
            func.max(Negotiation.created_at).label('last_activity'),
            func.count(Negotiation.id).label('message_count')
        ).join(Produce, Negotiation.produce_id == Produce.id)\
         .filter(Produce.farmer_id == user_id)\
         .group_by(Negotiation.produce_id)
    else:
        # For buyers, get negotiations they initiated
        negotiations_query = db.query(
            Negotiation.produce_id,
            func.min(Negotiation.created_at).label('started_on'),
            func.max(Negotiation.created_at).label('last_activity'),
            func.count(Negotiation.id).label('message_count')
        ).filter(Negotiation.sender_id == user_id)\
         .group_by(Negotiation.produce_id)

    negotiations_data = negotiations_query.all()

    summaries = []
    for neg_data in negotiations_data:
        # Get the latest negotiation for status determination
        latest_negotiation = db.query(Negotiation)\
            .filter(Negotiation.produce_id == neg_data.produce_id)\
            .order_by(desc(Negotiation.created_at))\
            .first()

        # Get produce and farmer details
        produce = db.query(Produce).options(joinedload(Produce.farmer)).filter(Produce.id == neg_data.produce_id).first()

        # Get buyer details (first buyer who made an offer)
        buyer = db.query(User).join(Negotiation, User.user_id == Negotiation.sender_id)\
            .filter(Negotiation.produce_id == neg_data.produce_id, Negotiation.sender_role == "buyer")\
            .first()

        # Determine status based on latest negotiation type
        status = NegotiationStatus.pending
        if latest_negotiation.type == "accept":
            status = NegotiationStatus.accepted
        elif latest_negotiation.type == "decline":
            status = NegotiationStatus.declined
        elif latest_negotiation.type == "reject":
            status = NegotiationStatus.rejected

        summary = NegotiationSummary(
            negotiation_id=latest_negotiation.id,
            produce_id=neg_data.produce_id,
            crop=produce.crop,
            category=produce.category,
            farmer_id=produce.farmer_id,
            farmer_name=produce.farmer.name if produce.farmer else "Unknown",
            buyer_id=buyer.user_id if buyer else None,
            buyer_name=buyer.name if buyer else None,
            status=status,
            started_on=neg_data.started_on,
            last_activity=neg_data.last_activity,
            original_price=produce.price_per_unit,
            current_offer_price=latest_negotiation.offered_price,
            offered_quantity=latest_negotiation.offered_quantity,
            delivery_location=latest_negotiation.delivery_location,
            message_count=neg_data.message_count
        )
        summaries.append(summary)

    return summaries

# Function to get negotiation summary by negotiation ID
def get_negotiation_summary_by_id(db: Session, negotiation_id: int) -> Optional[NegotiationSummary]:
    negotiation = db.query(Negotiation).filter(Negotiation.id == negotiation_id).first()
    if not negotiation:
        return None

    return get_negotiation_summaries_by_user(db, negotiation.sender_id, negotiation.sender_role)
