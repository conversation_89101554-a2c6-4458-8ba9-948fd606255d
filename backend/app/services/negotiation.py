#   Description: Service layer for handling negotiation-related operations.
# File: backend/app/services/negotiation.py

# import necessary modules and classses
from sqlalchemy.orm import Session
from app.models.negotiation import Negotiation
from app.schemas.negotiation import NegotiationCreate
from sqlalchemy import func, distinct

# Function to create a new negotiation offer
def create_offer(db: Session, sender_id: int, sender_role: str, offer_data: NegotiationCreate):
    negotiation = Negotiation(
        produce_id=offer_data.produce_id,
        sender_id=sender_id,
        sender_role=sender_role,
        message=offer_data.message,
        type=offer_data.type,
    )
    db.add(negotiation)
    db.commit()
    db.refresh(negotiation)
    return negotiation

# Function to get all offers for a specific produce
def get_offers_by_produce(db: Session, produce_id: int):
    return db.query(Negotiation).filter(Negotiation.produce_id == produce_id).order_by(Negotiation.created_at.asc()).all()

# Function to get all negotiations for a specific user
def get_negotiations_by_user(db: Session, user_id: int):
    # Get distinct produce IDs where the user is involved in negotiations
    # This includes both negotiations sent by the user and negotiations about the user's produce
    produce_ids = db.query(distinct(Negotiation.produce_id)).filter(
        Negotiation.sender_id == user_id
    ).all()

    # Flatten the list of tuples into a simple list of produce IDs
    produce_ids = [pid[0] for pid in produce_ids]

    result = []
    # For each produce, get all related negotiations
    for pid in produce_ids:
        negotiations = get_offers_by_produce(db, pid)
        if negotiations:
            result.extend(negotiations)

    return result
