# Description: This code defines the service functions for managing agricultural produce listings, including creating, listing, and retrieving produce by ID.
# File: backend/app/services/produce.py

# import necessary libraries
from sqlalchemy.orm import Session, joinedload
from app.models.produce import Produce
from app.models.user import User
from app.schemas.produce import ProduceCreate, ProduceUpdate
from typing import List, Optional

# Function to create a new produce listing
def create_produce(db: Session, produce: ProduceCreate) -> Produce:
    db_produce = Produce(**produce.model_dump())
    db.add(db_produce)
    db.commit()
    db.refresh(db_produce)
    return db_produce

# Function to list all produce listings
def list_produce(db: Session) -> List[dict]:
    produces = db.query(Produce).options(joinedload(Produce.farmer)).all()
    result = []
    for produce in produces:
        produce_dict = {
            "id": produce.id,
            "farmer_id": produce.farmer_id,
            "crop": produce.crop,
            "category": produce.category,
            "price_per_unit": produce.price_per_unit,
            "unit": produce.unit,
            "quantity": produce.quantity,
            "grade": produce.grade,
            "lat": produce.lat,
            "lon": produce.lon,
            "tags": produce.tags,
            "listing_date": produce.listing_date,
            "description": produce.description,
            "image_url": produce.image_url,
            "status": produce.status,
            "location": produce.location,
            "farmer_name": produce.farmer.name if produce.farmer else None
        }
        result.append(produce_dict)
    return result

# Function to get produce by ID
def get_produce_by_id(db: Session, produce_id: int) -> Optional[dict]:
    produce = db.query(Produce).options(joinedload(Produce.farmer)).filter(Produce.id == produce_id).first()
    if not produce:
        return None

    return {
        "id": produce.id,
        "farmer_id": produce.farmer_id,
        "crop": produce.crop,
        "category": produce.category,
        "price_per_unit": produce.price_per_unit,
        "unit": produce.unit,
        "quantity": produce.quantity,
        "grade": produce.grade,
        "lat": produce.lat,
        "lon": produce.lon,
        "tags": produce.tags,
        "listing_date": produce.listing_date,
        "description": produce.description,
        "image_url": produce.image_url,
        "status": produce.status,
        "location": produce.location,
        "farmer_name": produce.farmer.name if produce.farmer else None
    }

# Function to update a produce listing
def update_produce(db: Session, produce_id: int, produce_data: ProduceUpdate) -> Optional[Produce]:
    produce = db.query(Produce).filter(Produce.id == produce_id).first()
    if not produce:
        return None

    # Update fields
    for field, value in produce_data.model_dump().items():
        setattr(produce, field, value)

    db.commit()
    db.refresh(produce)
    return produce

# Function to delete a produce listing
def delete_produce(db: Session, produce_id: int) -> bool:
    produce = db.query(Produce).filter(Produce.id == produce_id).first()
    if not produce:
        return False

    db.delete(produce)
    db.commit()
    return True

# Function to get produce by farmer ID
def get_produce_by_farmer(db: Session, farmer_id: int) -> List[Produce]:
    return db.query(Produce).filter(Produce.farmer_id == farmer_id).all()
