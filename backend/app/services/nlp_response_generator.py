# Description: FastAPI router for handling negotiation suggestions using NLP.
# File: negotiation_assistant.py

# import necessary modules
from typing import List
#from sklearn.externals import joblib
import joblib

model = joblib.load("ml/models/negotiation_classifier.pkl")

templates = {
    "price_negotiation": "Can you offer ₹{price}?",
    "accept_offer": "Looks good, I accept the offer.",
    "decline_offer": "Sorry, your price is too low.",
    "counter_offer": "Can you meet me at ₹{price}?"
}

# Function to generate negotiation suggestions based on user history and role
def generate_suggestions(history: List[str], role: str) -> List[str]:
    last_message = history[-1]
    intent = model.predict([last_message])[0]

    # Dummy value injection logic (replace with context later)
    if intent == "price_negotiation":
        return [templates[intent].format(price=18)]
    elif intent == "counter_offer":
        return [templates[intent].format(price=19.5)]
    return [templates[intent]]
