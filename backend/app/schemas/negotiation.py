# Description: This module defines the schemas for negotiation offers in a Pydantic model.
# File: backend/app/schemas/negotiation.py

# import necessary modules and classes
from pydantic import BaseModel
from enum import Enum
from datetime import datetime
from typing import Optional

class NegotiationType(str, Enum):
    propose = "propose"
    accept = "accept"
    decline = "decline"
    reject = "reject"

class NegotiationStatus(str, Enum):
    pending = "pending"
    accepted = "accepted"
    declined = "declined"
    rejected = "rejected"

class NegotiationCreate(BaseModel):
    produce_id: int
    message: str
    type: NegotiationType
    offered_price: Optional[float] = None
    offered_quantity: Optional[float] = None
    delivery_location: Optional[str] = None

class NegotiationOut(BaseModel):
    id: int
    produce_id: int
    sender_id: int
    sender_role: str
    message: str
    type: NegotiationType
    offered_price: Optional[float] = None
    offered_quantity: Optional[float] = None
    delivery_location: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True  # use this instead of orm_mode in Pydantic v2

class NegotiationWithDetails(BaseModel):
    id: int
    produce_id: int
    sender_id: int
    sender_role: str
    sender_name: str
    message: str
    type: NegotiationType
    offered_price: Optional[float] = None
    offered_quantity: Optional[float] = None
    delivery_location: Optional[str] = None
    created_at: datetime

    # Produce details
    crop: str
    category: str
    original_price: float
    unit: str
    available_quantity: float
    farmer_name: str
    produce_location: Optional[str] = None

    class Config:
        from_attributes = True

class NegotiationSummary(BaseModel):
    negotiation_id: int
    produce_id: int
    crop: str
    category: str
    farmer_id: int
    farmer_name: str
    buyer_id: Optional[int] = None
    buyer_name: Optional[str] = None
    status: NegotiationStatus
    started_on: datetime
    last_activity: datetime
    original_price: float
    current_offer_price: Optional[float] = None
    offered_quantity: Optional[float] = None
    delivery_location: Optional[str] = None
    message_count: int

    class Config:
        from_attributes = True