# Description: This module defines the schemas for negotiation offers in a Pydantic model.
# File: backend/app/schemas/negotiation.py

# import necessary modules and classes
from pydantic import BaseModel
from enum import Enum
from datetime import datetime
from typing import Optional

class NegotiationType(str, Enum):
    propose = "propose"
    accept = "accept"
    decline = "decline"

class NegotiationCreate(BaseModel):
    produce_id: int
    message: str
    type: NegotiationType

class NegotiationOut(BaseModel):
    id: int
    produce_id: int
    sender_id: int
    sender_role: str
    message: str
    type: NegotiationType
    created_at: datetime

    class Config:
        from_attributes = True  # use this instead of orm_mode in Pydantic v2